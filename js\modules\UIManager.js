// UI管理模块
import { APP_CONFIG } from './config.js';

export class UIManager {
    constructor() {
        this.currentTheme = 'romantic';
        this.toastQueue = [];
        this.isShowingToast = false;
        this.modalStack = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.applyTheme(this.currentTheme);
    }
    
    setupEventListeners() {
        // 监听音频错误事件
        window.addEventListener('audioError', (e) => {
            this.showToast(e.detail.message);
        });
        
        // 监听主题变化事件
        window.addEventListener('themeChanged', (e) => {
            this.applyTheme(e.detail.theme);
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 150));
        
        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
    }
    
    // 显示Toast消息
    showToast(message, type = 'info', duration = APP_CONFIG.ANIMATION.TOAST_DURATION) {
        const toast = {
            message,
            type,
            duration,
            id: Date.now() + Math.random()
        };
        
        this.toastQueue.push(toast);
        this.processToastQueue();
    }
    
    processToastQueue() {
        if (this.isShowingToast || this.toastQueue.length === 0) {
            return;
        }
        
        this.isShowingToast = true;
        const toast = this.toastQueue.shift();
        this.displayToast(toast);
    }
    
    displayToast(toast) {
        const toastElement = this.createToastElement(toast);
        document.body.appendChild(toastElement);
        
        // 入场动画
        gsap.fromTo(toastElement, {
            opacity: 0,
            y: 20
        }, {
            opacity: 1,
            y: 0,
            duration: 0.3,
            ease: "power2.out"
        });
        
        // 自动消失
        setTimeout(() => {
            this.hideToast(toastElement);
        }, toast.duration);
    }
    
    createToastElement(toast) {
        const element = document.createElement('div');
        element.className = `toast toast-${toast.type}`;
        element.textContent = toast.message;
        element.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 0.9rem;
            z-index: 9999;
            max-width: 80%;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        `;
        
        // 根据类型设置不同颜色
        switch (toast.type) {
            case 'success':
                element.style.background = 'rgba(76, 175, 80, 0.9)';
                break;
            case 'error':
                element.style.background = 'rgba(244, 67, 54, 0.9)';
                break;
            case 'warning':
                element.style.background = 'rgba(255, 152, 0, 0.9)';
                break;
        }
        
        return element;
    }
    
    hideToast(toastElement) {
        gsap.to(toastElement, {
            opacity: 0,
            y: -20,
            duration: 0.3,
            ease: "power2.out",
            onComplete: () => {
                if (document.body.contains(toastElement)) {
                    document.body.removeChild(toastElement);
                }
                this.isShowingToast = false;
                this.processToastQueue();
            }
        });
    }
    
    // 模态框管理
    showModal(modalId, options = {}) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        const content = modal.querySelector('.modal-content');
        if (!content) return;
        
        // 添加到模态框栈
        this.modalStack.push(modalId);
        
        // 显示模态框
        modal.classList.add('active');
        
        // 动画效果
        gsap.fromTo(content, {
            scale: 0.8,
            opacity: 0
        }, {
            scale: 1,
            opacity: 1,
            duration: APP_CONFIG.ANIMATION.MODAL_ANIMATION_DURATION / 1000,
            ease: options.ease || "back.out(1.7)"
        });
        
        // 添加ESC键关闭功能
        this.addModalKeyListener(modalId);
    }
    
    hideModal(modalId, callback) {
        const modal = document.getElementById(modalId);
        if (!modal) return;
        
        const content = modal.querySelector('.modal-content');
        if (!content) return;
        
        // 从模态框栈移除
        const index = this.modalStack.indexOf(modalId);
        if (index > -1) {
            this.modalStack.splice(index, 1);
        }
        
        // 动画效果
        gsap.to(content, {
            scale: 0.8,
            opacity: 0,
            duration: APP_CONFIG.ANIMATION.MODAL_ANIMATION_DURATION / 1000,
            ease: "power2.inOut",
            onComplete: () => {
                modal.classList.remove('active');
                if (callback) callback();
            }
        });
    }
    
    addModalKeyListener(modalId) {
        const keyHandler = (e) => {
            if (e.key === 'Escape' && this.modalStack[this.modalStack.length - 1] === modalId) {
                this.hideModal(modalId);
                document.removeEventListener('keydown', keyHandler);
            }
        };
        
        document.addEventListener('keydown', keyHandler);
    }
    
    // 主题管理
    applyTheme(themeName) {
        if (!APP_CONFIG.THEMES[themeName]) return;
        
        const theme = APP_CONFIG.THEMES[themeName];
        this.currentTheme = themeName;
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--primary-color', theme.primary);
        document.documentElement.style.setProperty('--secondary-color', theme.secondary);
        document.documentElement.style.setProperty('--accent-color', theme.accent);
        
        // 更新背景渐变
        document.body.style.background = theme.background;
        
        // 触发主题变化事件
        window.dispatchEvent(new CustomEvent('themeApplied', {
            detail: { theme: themeName, colors: theme }
        }));
        
        this.showToast(`已切换到${theme.name}主题`, 'success');
    }
    
    // 进度指示器更新
    updateProgress(current, total) {
        const progressText = document.getElementById('progress-text');
        if (progressText) {
            progressText.textContent = `${current}/${total}`;
        }
    }
    
    // 心形提示更新
    updateHeartPrompt(text) {
        const heartPrompt = document.getElementById('heart-prompt');
        if (heartPrompt) {
            const span = heartPrompt.querySelector('span');
            if (span) {
                span.textContent = text;
            }
        }
    }
    
    // 音乐按钮状态更新
    updateMusicButton(isPlaying) {
        const musicIcon = document.getElementById('music-icon');
        const musicStatus = document.getElementById('music-status');
        const musicToggle = document.getElementById('music-toggle');
        
        if (!musicIcon || !musicStatus || !musicToggle) return;
        
        if (isPlaying) {
            musicIcon.textContent = '🎶';
            musicStatus.textContent = '播放中';
            musicToggle.classList.add('music-active');
        } else {
            musicIcon.textContent = '🔇';
            musicStatus.textContent = '已静音';
            musicToggle.classList.remove('music-active');
        }
    }
    
    // 安装按钮显示/隐藏
    showInstallButton() {
        const installBtn = document.getElementById('install-btn');
        if (installBtn) {
            installBtn.style.display = 'flex';
        }
    }
    
    hideInstallButton() {
        const installBtn = document.getElementById('install-btn');
        if (installBtn) {
            installBtn.style.display = 'none';
        }
    }
    
    // 加载状态管理
    showLoading(target) {
        if (typeof target === 'string') {
            target = document.getElementById(target);
        }
        
        if (!target) return;
        
        const loader = document.createElement('div');
        loader.className = 'loading';
        loader.innerHTML = '<div class="spinner"></div>';
        
        target.appendChild(loader);
        target.classList.add('loading-state');
    }
    
    hideLoading(target) {
        if (typeof target === 'string') {
            target = document.getElementById(target);
        }
        
        if (!target) return;
        
        const loader = target.querySelector('.loading');
        if (loader) {
            target.removeChild(loader);
        }
        
        target.classList.remove('loading-state');
    }
    
    // 响应式处理
    handleResize() {
        // 更新画布大小
        window.dispatchEvent(new CustomEvent('canvasResize'));
        
        // 更新粒子容器
        this.updateParticleContainer();
        
        // 更新模态框位置
        this.updateModalPositions();
    }
    
    updateParticleContainer() {
        const container = document.getElementById('particles-container');
        if (container) {
            // 清除现有粒子，重新创建
            window.dispatchEvent(new CustomEvent('particlesUpdate'));
        }
    }
    
    updateModalPositions() {
        // 确保模态框在屏幕中央
        const activeModals = document.querySelectorAll('.modal.active');
        activeModals.forEach(modal => {
            const content = modal.querySelector('.modal-content');
            if (content) {
                // 重新计算位置
                content.style.maxHeight = '80vh';
            }
        });
    }
    
    // 键盘事件处理
    handleKeydown(e) {
        // ESC键关闭最顶层模态框
        if (e.key === 'Escape' && this.modalStack.length > 0) {
            const topModal = this.modalStack[this.modalStack.length - 1];
            this.hideModal(topModal);
        }
        
        // 空格键暂停/播放音乐
        if (e.key === ' ' && e.target.tagName !== 'INPUT') {
            e.preventDefault();
            window.dispatchEvent(new CustomEvent('toggleMusic'));
        }
    }
    
    // 截图闪光效果
    showScreenshotFlash() {
        const flash = document.createElement('div');
        flash.className = 'screenshot-flash';
        document.body.appendChild(flash);
        
        setTimeout(() => {
            if (document.body.contains(flash)) {
                document.body.removeChild(flash);
            }
        }, 300);
    }
    
    // 工具函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // 获取当前主题
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    // 获取UI状态
    getState() {
        return {
            currentTheme: this.currentTheme,
            modalStack: [...this.modalStack],
            toastQueueLength: this.toastQueue.length,
            isShowingToast: this.isShowingToast
        };
    }
    
    // 清理资源
    cleanup() {
        // 清除所有Toast
        this.toastQueue = [];
        document.querySelectorAll('.toast').forEach(toast => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        });
        
        // 关闭所有模态框
        this.modalStack.forEach(modalId => {
            this.hideModal(modalId);
        });
        
        // 移除事件监听器
        window.removeEventListener('audioError', this.handleAudioError);
        window.removeEventListener('themeChanged', this.handleThemeChanged);
        window.removeEventListener('resize', this.handleResize);
        document.removeEventListener('keydown', this.handleKeydown);
    }
}
