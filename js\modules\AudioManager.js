// 音频管理模块
import { APP_CONFIG } from './config.js';

export class AudioManager {
    constructor(audioElementId) {
        this.audio = document.getElementById(audioElementId);
        this.isEnabled = false;
        this.isLoaded = false;
        this.hasTriedAutoPlay = false;
        this.volume = APP_CONFIG.AUDIO.DEFAULT_VOLUME;
        this.fadeInterval = null;
        
        this.init();
    }
    
    init() {
        if (!this.audio) {
            console.warn('Audio element not found, audio features disabled');
            return;
        }

        this.setupEventListeners();
        this.loadUserPreferences();
        console.log('AudioManager initialized');
    }
    
    setupEventListeners() {
        if (!this.audio) return;
        
        // 音频加载事件
        this.audio.addEventListener('canplaythrough', () => {
            this.isLoaded = true;
            console.log('Audio file loaded successfully');
        });
        
        this.audio.addEventListener('error', (e) => {
            console.error('Audio file failed to load:', e);
            this.handleAudioError(e);
        });
        
        // 播放状态事件
        this.audio.addEventListener('play', () => {
            this.isEnabled = true;
            this.updateUI(true);
        });
        
        this.audio.addEventListener('pause', () => {
            this.isEnabled = false;
            this.updateUI(false);
        });
        
        this.audio.addEventListener('ended', () => {
            // 循环播放
            if (this.isEnabled) {
                this.audio.currentTime = 0;
                this.play();
            }
        });
        
        // 音量变化事件
        this.audio.addEventListener('volumechange', () => {
            this.volume = this.audio.volume;
        });
    }
    
    // 尝试自动播放
    tryAutoPlay() {
        if (this.hasTriedAutoPlay || !this.audio) return;
        
        this.hasTriedAutoPlay = true;
        this.audio.volume = this.volume;
        
        const playPromise = this.audio.play();
        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log('Auto-play successful');
                this.savePreference(true);
            }).catch(e => {
                console.log('Auto-play failed (normal):', e);
                this.showUnlockPrompt();
            });
        }
    }
    
    // 播放音乐
    async play() {
        if (!this.audio || !this.isLoaded) {
            throw new Error(APP_CONFIG.ERROR_MESSAGES.AUDIO_LOAD_FAILED);
        }
        
        try {
            this.audio.volume = this.volume;
            await this.audio.play();
            this.savePreference(true);
            return true;
        } catch (error) {
            this.handlePlayError(error);
            throw error;
        }
    }
    
    // 暂停音乐
    pause() {
        if (this.audio && !this.audio.paused) {
            this.audio.pause();
            this.savePreference(false);
        }
    }
    
    // 切换播放状态
    async toggle() {
        if (this.isEnabled) {
            this.pause();
        } else {
            await this.play();
        }
    }
    
    // 设置音量
    setVolume(volume) {
        if (this.audio) {
            this.volume = Math.max(0, Math.min(1, volume));
            this.audio.volume = this.volume;
        }
    }
    
    // 淡入
    fadeIn(duration = APP_CONFIG.AUDIO.FADE_DURATION) {
        if (!this.audio) return;
        
        this.clearFade();
        this.audio.volume = 0;
        
        const targetVolume = this.volume;
        const steps = 50;
        const stepTime = duration / steps;
        const volumeStep = targetVolume / steps;
        
        let currentStep = 0;
        this.fadeInterval = setInterval(() => {
            currentStep++;
            this.audio.volume = Math.min(volumeStep * currentStep, targetVolume);
            
            if (currentStep >= steps) {
                this.clearFade();
            }
        }, stepTime);
    }
    
    // 淡出
    fadeOut(duration = APP_CONFIG.AUDIO.FADE_DURATION) {
        if (!this.audio) return;
        
        this.clearFade();
        const startVolume = this.audio.volume;
        const steps = 50;
        const stepTime = duration / steps;
        const volumeStep = startVolume / steps;
        
        let currentStep = 0;
        this.fadeInterval = setInterval(() => {
            currentStep++;
            this.audio.volume = Math.max(startVolume - (volumeStep * currentStep), 0);
            
            if (currentStep >= steps) {
                this.clearFade();
                this.pause();
            }
        }, stepTime);
    }
    
    // 清除淡入淡出
    clearFade() {
        if (this.fadeInterval) {
            clearInterval(this.fadeInterval);
            this.fadeInterval = null;
        }
    }
    
    // 处理播放错误
    handlePlayError(error) {
        let message = APP_CONFIG.ERROR_MESSAGES.AUDIO_PLAY_FAILED;
        
        switch (error.name) {
            case 'AbortError':
            case 'NotSupportedError':
                message = APP_CONFIG.ERROR_MESSAGES.AUDIO_NOT_SUPPORTED;
                break;
            case 'NotAllowedError':
                message = APP_CONFIG.ERROR_MESSAGES.AUDIO_NOT_ALLOWED;
                break;
            case 'NetworkError':
                message = APP_CONFIG.ERROR_MESSAGES.AUDIO_NETWORK_ERROR;
                break;
            default:
                message = `${APP_CONFIG.ERROR_MESSAGES.AUDIO_PLAY_FAILED}，错误代码: ${error.name}`;
        }
        
        this.showError(message);
    }
    
    // 处理音频加载错误
    handleAudioError(error) {
        this.showError(APP_CONFIG.ERROR_MESSAGES.AUDIO_LOAD_FAILED);
    }
    
    // 显示解锁提示
    showUnlockPrompt() {
        const musicToggle = document.getElementById('music-toggle');
        if (!musicToggle) return;
        
        const originalHTML = musicToggle.innerHTML;
        
        // 创建解锁提示
        musicToggle.innerHTML = '<span id="music-icon">🔒</span><span id="music-status">点击解锁音乐</span>';
        musicToggle.classList.add('music-unlock');
        
        // 添加一次性点击事件
        const unlockHandler = async () => {
            try {
                await this.play();
                console.log('User unlocked music');
                musicToggle.classList.remove('music-unlock');
                musicToggle.innerHTML = originalHTML;
                musicToggle.removeEventListener('click', unlockHandler);
            } catch (error) {
                console.error('User unlock failed', error);
                this.showError(APP_CONFIG.ERROR_MESSAGES.AUDIO_PLAY_FAILED);
            }
        };
        
        musicToggle.addEventListener('click', unlockHandler);
    }
    
    // 更新UI
    updateUI(isPlaying) {
        const musicIcon = document.getElementById('music-icon');
        const musicStatus = document.getElementById('music-status');
        const musicToggle = document.getElementById('music-toggle');
        
        if (!musicIcon || !musicStatus || !musicToggle) return;
        
        if (isPlaying) {
            musicIcon.textContent = '🎶';
            musicStatus.textContent = '播放中';
            musicToggle.classList.add('music-active');
        } else {
            musicIcon.textContent = '🔇';
            musicStatus.textContent = '已静音';
            musicToggle.classList.remove('music-active');
        }
    }
    
    // 显示错误信息
    showError(message) {
        // 触发自定义事件，让UI管理器处理
        window.dispatchEvent(new CustomEvent('audioError', {
            detail: { message }
        }));
    }
    
    // 保存用户偏好
    savePreference(enabled) {
        localStorage.setItem(APP_CONFIG.STORAGE_KEYS.MUSIC_ENABLED, enabled);
    }
    
    // 加载用户偏好
    loadUserPreferences() {
        const enabled = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.MUSIC_ENABLED) === 'true';
        if (enabled) {
            this.isEnabled = true;
            const musicToggle = document.getElementById('music-toggle');
            if (musicToggle) {
                musicToggle.classList.add('music-active');
            }
        }
    }
    
    // 获取音频状态
    getStatus() {
        return {
            isEnabled: this.isEnabled,
            isLoaded: this.isLoaded,
            volume: this.volume,
            currentTime: this.audio ? this.audio.currentTime : 0,
            duration: this.audio ? this.audio.duration : 0,
            paused: this.audio ? this.audio.paused : true
        };
    }
    
    // 设置循环播放
    setLoop(loop) {
        if (this.audio) {
            this.audio.loop = loop;
        }
    }
    
    // 设置播放速度
    setPlaybackRate(rate) {
        if (this.audio) {
            this.audio.playbackRate = Math.max(0.25, Math.min(4, rate));
        }
    }
    
    // 跳转到指定时间
    seekTo(time) {
        if (this.audio && this.isLoaded) {
            this.audio.currentTime = Math.max(0, Math.min(this.audio.duration, time));
        }
    }
    
    // 销毁
    destroy() {
        this.clearFade();
        this.pause();
        
        if (this.audio) {
            this.audio.removeEventListener('canplaythrough', this.handleCanPlayThrough);
            this.audio.removeEventListener('error', this.handleError);
            this.audio.removeEventListener('play', this.handlePlay);
            this.audio.removeEventListener('pause', this.handlePause);
            this.audio.removeEventListener('ended', this.handleEnded);
            this.audio.removeEventListener('volumechange', this.handleVolumeChange);
        }
        
        this.audio = null;
    }
}
