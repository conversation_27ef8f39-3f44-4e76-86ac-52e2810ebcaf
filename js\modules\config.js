// 应用配置文件
export const APP_CONFIG = {
    // 应用基本信息
    APP_NAME: '心动告白',
    VERSION: '2.0.0',
    
    // 动画配置
    ANIMATION: {
        HEART_EXPLOSION_DURATION: 600,
        MESSAGE_FADE_DURATION: 500,
        PARTICLE_FLOAT_DURATION: 2000,
        RIPPLE_DURATION: 600,
        TOAST_DURATION: 2000,
        MODAL_ANIMATION_DURATION: 300
    },
    
    // 性能配置
    PERFORMANCE: {
        MAX_PARTICLES_HIGH: 20,
        MAX_PARTICLES_LOW: 5,
        MAX_EXPLOSION_PARTICLES_HIGH: 12,
        MAX_EXPLOSION_PARTICLES_LOW: 6,
        MEMORY_CLEANUP_INTERVAL: 10000,
        RESIZE_DEBOUNCE_DELAY: 150,
        CLICK_DEBOUNCE_DELAY: 100
    },
    
    // 画布配置
    CANVAS: {
        MAX_SIZE: 300,
        MIN_SIZE: 200,
        HEART_SCALE_FACTOR: 400,
        DPR_ENABLED: true
    },
    
    // 音频配置
    AUDIO: {
        DEFAULT_VOLUME: 0.5,
        FADE_DURATION: 1000,
        AUTO_PLAY_DELAY: 1000
    },
    
    // 存储键名
    STORAGE_KEYS: {
        MUSIC_ENABLED: 'heartapp_music_enabled',
        CUSTOM_MESSAGES: 'heartapp_custom_messages',
        INSTALL_GUIDE_SHOWN: 'installGuideShown',
        THEME_PREFERENCE: 'heartapp_theme',
        USER_PREFERENCES: 'heartapp_preferences'
    },
    
    // 默认告白消息
    DEFAULT_MESSAGES: [
        "你知道吗？你的笑容是我见过最美的风景。",
        "每当我看到你，我的心跳都会不由自主地加速。",
        "和你在一起的每一刻，都让我感到无比幸福。",
        "你的温柔如春风，轻抚着我的心田。",
        "我想牵着你的手，走过人生的每一个春夏秋冬。",
        "我爱你，不只是今天，而是每一个明天。"
    ],
    
    // 主题配置
    THEMES: {
        romantic: {
            name: '浪漫粉',
            primary: '#ff6b9d',
            secondary: '#c44569',
            accent: '#f8b500',
            background: 'linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%)'
        },
        sunset: {
            name: '夕阳橙',
            primary: '#ff7675',
            secondary: '#fd79a8',
            accent: '#fdcb6e',
            background: 'linear-gradient(135deg, #ff7675 0%, #fd79a8 50%, #fdcb6e 100%)'
        },
        ocean: {
            name: '海洋蓝',
            primary: '#74b9ff',
            secondary: '#0984e3',
            accent: '#00cec9',
            background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #00cec9 100%)'
        },
        forest: {
            name: '森林绿',
            primary: '#00b894',
            secondary: '#00a085',
            accent: '#55a3ff',
            background: 'linear-gradient(135deg, #00b894 0%, #00a085 50%, #55a3ff 100%)'
        }
    },
    
    // PWA配置
    PWA: {
        INSTALL_GUIDE_DELAY: 10000,
        CACHE_VERSION: 'v2.0.0'
    },
    
    // 错误消息
    ERROR_MESSAGES: {
        AUDIO_LOAD_FAILED: '音乐文件加载失败，请检查文件路径',
        AUDIO_PLAY_FAILED: '音乐播放失败',
        AUDIO_NOT_SUPPORTED: '浏览器不支持此音频格式',
        AUDIO_NOT_ALLOWED: '请允许浏览器播放音乐',
        AUDIO_NETWORK_ERROR: '音乐文件加载失败 (404)',
        SHARE_FAILED: '分享失败，请手动复制链接',
        SCREENSHOT_FAILED: '请使用浏览器的截图功能',
        INSTALL_CANCELLED: '安装已取消',
        SAVE_FAILED: '保存失败，请重试',
        LOAD_FAILED: '加载失败，请刷新页面'
    },
    
    // 成功消息
    SUCCESS_MESSAGES: {
        CUSTOM_MESSAGES_SAVED: '自定义消息已保存',
        DEFAULT_MESSAGES_RESTORED: '已恢复默认消息',
        LINK_COPIED: '链接已复制到剪贴板',
        APP_INSTALLED: '应用已添加到桌面',
        THEME_CHANGED: '主题已切换',
        PREFERENCES_SAVED: '设置已保存'
    },
    
    // 设备检测
    DEVICE_DETECTION: {
        OLD_DEVICE_PATTERNS: /android\s[1-4]|iphone\sos\s[1-9]|windows\sphone/i,
        MOBILE_PATTERNS: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i,
        LOW_MEMORY_THRESHOLD: 4,
        SLOW_CONNECTION_TYPE: 'slow-2g'
    }
};

// 工具函数
export const UTILS = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 随机数生成
    random(min, max) {
        return Math.random() * (max - min) + min;
    },
    
    // 角度转弧度
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    },
    
    // 弧度转角度
    toDegrees(radians) {
        return radians * (180 / Math.PI);
    },
    
    // 深拷贝
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    },
    
    // 格式化时间
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    },
    
    // 检测设备类型
    isMobile() {
        return APP_CONFIG.DEVICE_DETECTION.MOBILE_PATTERNS.test(navigator.userAgent);
    },
    
    // 检测是否为低性能设备
    isLowPerformanceDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isOldDevice = APP_CONFIG.DEVICE_DETECTION.OLD_DEVICE_PATTERNS.test(userAgent);
        const hasLowRAM = navigator.deviceMemory && navigator.deviceMemory < APP_CONFIG.DEVICE_DETECTION.LOW_MEMORY_THRESHOLD;
        const hasSlowConnection = navigator.connection && navigator.connection.effectiveType === APP_CONFIG.DEVICE_DETECTION.SLOW_CONNECTION_TYPE;
        
        return isOldDevice || hasLowRAM || hasSlowConnection;
    }
};
