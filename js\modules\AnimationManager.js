// 动画管理模块
import { APP_CONFIG, UTILS } from './config.js';

export class AnimationManager {
    constructor() {
        this.activeAnimations = new Map();
        this.particlePool = [];
        this.isLowPerformance = UTILS.isLowPerformanceDevice();
        this.maxParticles = this.isLowPerformance ? 
            APP_CONFIG.PERFORMANCE.MAX_PARTICLES_LOW : 
            APP_CONFIG.PERFORMANCE.MAX_PARTICLES_HIGH;
        this.maxExplosionParticles = this.isLowPerformance ? 
            APP_CONFIG.PERFORMANCE.MAX_EXPLOSION_PARTICLES_LOW : 
            APP_CONFIG.PERFORMANCE.MAX_EXPLOSION_PARTICLES_HIGH;
    }
    
    // 创建点击波纹效果
    createClickRipple(event, canvas) {
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const ripple = this.createRippleElement(rect, x, y);
        document.body.appendChild(ripple);
        
        // 使用GSAP动画
        gsap.to(ripple, {
            width: '100px',
            height: '100px',
            opacity: 0,
            duration: APP_CONFIG.ANIMATION.RIPPLE_DURATION / 1000,
            ease: "power2.out",
            onComplete: () => {
                if (document.body.contains(ripple)) {
                    document.body.removeChild(ripple);
                }
            }
        });
        
        return ripple;
    }
    
    createRippleElement(rect, x, y) {
        const ripple = document.createElement('div');
        ripple.style.cssText = `
            position: absolute;
            left: ${rect.left + x}px;
            top: ${rect.top + y}px;
            width: 0px;
            height: 0px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: translate(-50%, -50%);
            pointer-events: none;
            z-index: 1000;
        `;
        return ripple;
    }
    
    // 创建爆炸粒子效果
    createExplosionParticles(canvas) {
        if (this.isLowPerformance) {
            return this.createSimpleExplosion(canvas);
        }
        
        const rect = canvas.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const particles = [];
        
        for (let i = 0; i < this.maxExplosionParticles; i++) {
            const particle = this.createExplosionParticle(centerX, centerY, i);
            particles.push(particle);
            document.body.appendChild(particle);
            
            this.animateExplosionParticle(particle, centerX, centerY, i);
        }
        
        return particles;
    }
    
    createExplosionParticle(centerX, centerY, index) {
        const particle = document.createElement('div');
        particle.innerHTML = '❤️';
        particle.style.cssText = `
            position: fixed;
            left: ${centerX}px;
            top: ${centerY}px;
            font-size: 1.5rem;
            pointer-events: none;
            z-index: 1000;
        `;
        return particle;
    }
    
    animateExplosionParticle(particle, centerX, centerY, index) {
        const angle = (index / this.maxExplosionParticles) * Math.PI * 2;
        const distance = 80 + Math.random() * 40;
        const endX = centerX + Math.cos(angle) * distance;
        const endY = centerY + Math.sin(angle) * distance;
        
        gsap.to(particle, {
            x: endX - centerX,
            y: endY - centerY,
            opacity: 0,
            scale: 0.5,
            duration: 1,
            ease: "power2.out",
            onComplete: () => {
                if (document.body.contains(particle)) {
                    document.body.removeChild(particle);
                }
            }
        });
    }
    
    createSimpleExplosion(canvas) {
        // 低性能设备的简单爆炸效果
        canvas.style.filter = 'brightness(1.2) saturate(1.2)';
        setTimeout(() => {
            canvas.style.filter = 'none';
        }, 200);
        return [];
    }
    
    // 创建漂浮心形粒子
    createHeartParticles() {
        const container = document.getElementById('particles-container');
        if (!container) return;
        
        const particleCount = this.isLowPerformance ? 3 : 6;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = this.createHeartParticle();
            container.appendChild(particle);
            this.animateHeartParticle(particle, container);
        }
    }
    
    createHeartParticle() {
        const particle = document.createElement('div');
        particle.innerHTML = '💕';
        particle.className = 'heart-particle';
        particle.style.cssText = `
            position: absolute;
            left: ${Math.random() * window.innerWidth}px;
            top: ${window.innerHeight}px;
            font-size: 1.5rem;
            pointer-events: none;
            z-index: 100;
        `;
        return particle;
    }
    
    animateHeartParticle(particle, container) {
        gsap.to(particle, {
            y: -200,
            x: (Math.random() - 0.5) * 100,
            opacity: 0,
            duration: APP_CONFIG.ANIMATION.PARTICLE_FLOAT_DURATION / 1000,
            ease: "power2.out",
            onComplete: () => {
                if (container.contains(particle)) {
                    container.removeChild(particle);
                }
            }
        });
    }
    
    // 创建背景粒子
    createBackgroundParticles() {
        const container = document.getElementById('particles-container');
        if (!container) return;
        
        // 清除现有粒子
        container.innerHTML = '';
        
        for (let i = 0; i < this.maxParticles; i++) {
            const particle = this.createBackgroundParticle();
            container.appendChild(particle);
        }
    }
    
    createBackgroundParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            left: ${Math.random() * window.innerWidth}px;
            top: ${Math.random() * window.innerHeight}px;
            animation: float ${3 + Math.random() * 2}s ease-in-out infinite;
            animation-delay: ${Math.random() * 3}s;
        `;
        return particle;
    }
    
    // 消息淡入动画
    animateMessageFade(messageElement, newText, callback) {
        gsap.to(messageElement, {
            opacity: 0,
            y: -10,
            duration: APP_CONFIG.ANIMATION.MESSAGE_FADE_DURATION / 2000,
            ease: "power2.out",
            onComplete: () => {
                messageElement.textContent = newText;
                gsap.to(messageElement, {
                    opacity: 1,
                    y: 0,
                    duration: APP_CONFIG.ANIMATION.MESSAGE_FADE_DURATION / 1000,
                    ease: "power2.out",
                    onComplete: callback
                });
            }
        });
    }
    
    // 元素脉冲动画
    animatePulse(element, options = {}) {
        const {
            scale = 1.1,
            duration = 0.2,
            repeat = 1,
            yoyo = true
        } = options;
        
        gsap.to(element, {
            scale,
            duration,
            yoyo,
            repeat,
            ease: "power2.inOut"
        });
    }
    
    // 弹跳动画
    animateBounce(element, options = {}) {
        const {
            y = -10,
            duration = 0.6
        } = options;
        
        element.classList.add('share-animation');
        
        gsap.to(element, {
            y,
            duration: duration / 2,
            ease: "power2.out",
            yoyo: true,
            repeat: 1,
            onComplete: () => {
                element.classList.remove('share-animation');
            }
        });
    }
    
    // 模态框动画
    animateModalOpen(modalElement, contentElement) {
        modalElement.classList.add('active');
        
        gsap.fromTo(contentElement, {
            scale: 0.8,
            opacity: 0
        }, {
            scale: 1,
            opacity: 1,
            duration: APP_CONFIG.ANIMATION.MODAL_ANIMATION_DURATION / 1000,
            ease: "back.out(1.7)"
        });
    }
    
    animateModalClose(modalElement, contentElement, callback) {
        gsap.to(contentElement, {
            scale: 0.8,
            opacity: 0,
            duration: APP_CONFIG.ANIMATION.MODAL_ANIMATION_DURATION / 1000,
            ease: "power2.inOut",
            onComplete: () => {
                modalElement.classList.remove('active');
                if (callback) callback();
            }
        });
    }
    
    // Toast动画
    animateToast(toastElement, message) {
        toastElement.textContent = message;
        toastElement.style.opacity = '0';
        document.body.appendChild(toastElement);
        
        gsap.to(toastElement, {
            opacity: 1,
            duration: 0.3,
            ease: "power2.out"
        });
        
        setTimeout(() => {
            gsap.to(toastElement, {
                opacity: 0,
                duration: 0.3,
                ease: "power2.out",
                onComplete: () => {
                    if (document.body.contains(toastElement)) {
                        document.body.removeChild(toastElement);
                    }
                }
            });
        }, APP_CONFIG.ANIMATION.TOAST_DURATION);
    }
    
    // 背景渐变动画
    animateBackgroundGradient() {
        gsap.to(document.body, {
            duration: 10,
            repeat: -1,
            yoyo: true,
            ease: "sine.inOut",
            background: "linear-gradient(135deg, #c44569 0%, #ff6b9d 50%, #ff8fab 100%)"
        });
    }
    
    // 清理动画
    cleanup() {
        // 清理所有活动动画
        this.activeAnimations.forEach((animation, key) => {
            if (animation.kill) {
                animation.kill();
            }
        });
        this.activeAnimations.clear();
        
        // 清理粒子
        const container = document.getElementById('particles-container');
        if (container) {
            container.innerHTML = '';
        }
        
        // 清理临时元素
        document.querySelectorAll('.heart-particle, .particle').forEach(el => {
            if (el.parentNode) {
                el.parentNode.removeChild(el);
            }
        });
    }
    
    // 暂停所有动画
    pauseAll() {
        gsap.globalTimeline.pause();
    }
    
    // 恢复所有动画
    resumeAll() {
        gsap.globalTimeline.resume();
    }
    
    // 获取性能信息
    getPerformanceInfo() {
        return {
            isLowPerformance: this.isLowPerformance,
            maxParticles: this.maxParticles,
            maxExplosionParticles: this.maxExplosionParticles,
            activeAnimations: this.activeAnimations.size
        };
    }
}
