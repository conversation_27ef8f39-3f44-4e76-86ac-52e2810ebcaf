// Heart Confession App - Main JavaScript (Refactored)
import { APP_CONFIG } from './modules/config.js';
import { HeartRenderer } from './modules/HeartRenderer.js';
import { AnimationManager } from './modules/AnimationManager.js';
import { AudioManager } from './modules/AudioManager.js';
import { StorageManager } from './modules/StorageManager.js';
import { UIManager } from './modules/UIManager.js';
import { PWAManager } from './modules/PWAManager.js';
import { PerformanceManager } from './modules/PerformanceManager.js';

class HeartConfessionApp {
    constructor() {
        this.currentMessage = 0;
        this.isAnimating = false;
        this.messages = [...APP_CONFIG.DEFAULT_MESSAGES];

        // 初始化管理器
        this.managers = {
            storage: new StorageManager(),
            ui: new UIManager(),
            performance: new PerformanceManager(),
            animation: new AnimationManager(),
            audio: new AudioManager('bg-music'),
            pwa: new PWAManager()
        };

        // 延迟初始化HeartRenderer，确保DOM已加载
        setTimeout(() => {
            console.log('Initializing HeartRenderer...');
            this.managers.heart = new HeartRenderer('heart-canvas');
        }, 100);

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadUserPreferences();
        this.setupCustomEventHandlers();

        // 初始化心形渲染
        if (this.managers.heart) {
            console.log('Initializing heart renderer...');
        }

        this.managers.animation.createBackgroundParticles();
        this.managers.animation.animateBackgroundGradient();

        console.log('Heart Confession App initialized successfully');
        console.log('Performance report:', this.managers.performance.getPerformanceReport());
    }

    setupCustomEventHandlers() {
        // 监听性能优化事件
        window.addEventListener('performanceOptimized', (e) => {
            console.log('Performance optimized:', e.detail);
        });

        // 监听主题应用事件
        window.addEventListener('themeApplied', (e) => {
            this.managers.heart.setTheme(e.detail.theme);
        });

        // 监听画布调整事件
        window.addEventListener('canvasResize', () => {
            this.managers.heart.resize();
        });

        // 监听粒子更新事件
        window.addEventListener('particlesUpdate', () => {
            this.managers.animation.createBackgroundParticles();
        });

        // 监听音乐切换事件
        window.addEventListener('toggleMusic', () => {
            this.toggleMusic();
        });

        // 监听Toast显示事件
        window.addEventListener('showToast', (e) => {
            this.managers.ui.showToast(e.detail.message, e.detail.type);
        });
    }
    
    // 设置事件监听器（简化版）
    
    setupEventListeners() {
        const canvas = document.getElementById('heart-canvas');

        // Heart click event
        canvas?.addEventListener('click', (e) => this.handleHeartClick(e));

        // Music toggle
        document.getElementById('music-toggle')?.addEventListener('click', () => this.toggleMusic());

        // Share button
        document.getElementById('share-btn')?.addEventListener('click', () => this.shareApp());

        // Install button
        document.getElementById('install-btn')?.addEventListener('click', () => this.installApp());

        // Screenshot button
        document.getElementById('screenshot-btn')?.addEventListener('click', () => this.takeScreenshot());

        // Settings button
        document.getElementById('settings-btn')?.addEventListener('click', () => this.openSettings());

        // Modal controls
        document.getElementById('save-custom')?.addEventListener('click', () => this.saveCustomMessages());
        document.getElementById('cancel-custom')?.addEventListener('click', () => this.closeSettings());
        document.getElementById('reset-default')?.addEventListener('click', () => this.resetToDefault());

        // Close modal on backdrop click
        document.getElementById('settings-modal')?.addEventListener('click', (e) => {
            if (e.target.id === 'settings-modal') {
                this.closeSettings();
            }
        });

        // Prevent context menu on long press
        canvas?.addEventListener('contextmenu', (e) => e.preventDefault());
    }
    
    // 心形点击处理
    
    handleHeartClick(event) {
        if (this.isAnimating) return;

        // 尝试自动播放音乐
        if (!this.managers.audio.hasTriedAutoPlay) {
            this.managers.audio.tryAutoPlay();
        }

        this.isAnimating = true;

        // 创建点击波纹效果
        const canvas = document.getElementById('heart-canvas');
        this.managers.animation.createClickRipple(event, canvas);

        // 心形爆炸动画
        this.managers.heart.animateExplosion();
        this.managers.animation.createExplosionParticles(canvas);

        // 显示下一条消息
        this.showNextMessage();

        // 创建漂浮心形粒子
        this.managers.animation.createHeartParticles();

        setTimeout(() => {
            this.isAnimating = false;
        }, APP_CONFIG.ANIMATION.HEART_EXPLOSION_DURATION);
    }
    
    showNextMessage() {
        const messageText = document.getElementById('message-text');
        const heartPrompt = document.getElementById('heart-prompt');

        // 更新当前消息索引
        this.currentMessage = (this.currentMessage + 1) % this.messages.length;
        const displayIndex = this.currentMessage === 0 ? this.messages.length : this.currentMessage;

        // 更新进度
        this.managers.ui.updateProgress(displayIndex, 6);

        // 更新消息文本
        this.managers.animation.animateMessageFade(
            messageText,
            this.messages[this.currentMessage]
        );

        // 更新心形提示
        const promptText = this.currentMessage === 0
            ? '点击❤️重新开始'
            : `继续点击❤️ (${6 - displayIndex}次)`;
        this.managers.ui.updateHeartPrompt(promptText);

        // 心形提示脉冲动画
        this.managers.animation.animatePulse(heartPrompt);
    }
    
    // 音乐控制
    
    async toggleMusic() {
        try {
            await this.managers.audio.toggle();
        } catch (error) {
            console.error('Music toggle failed:', error);
            this.managers.ui.showToast(error.message, 'error');
        }
    }
    
    shareApp() {
        const shareBtn = document.getElementById('share-btn');
        this.managers.animation.animateBounce(shareBtn);

        if (navigator.share) {
            navigator.share({
                title: APP_CONFIG.APP_NAME,
                text: '用这个浪漫的告白应用表达你的心意吧！',
                url: window.location.href
            }).catch(err => {
                console.log('Share failed:', err);
                this.managers.ui.showToast(APP_CONFIG.ERROR_MESSAGES.SHARE_FAILED, 'error');
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.managers.ui.showToast(APP_CONFIG.SUCCESS_MESSAGES.LINK_COPIED, 'success');
            }).catch(() => {
                this.managers.ui.showToast(APP_CONFIG.ERROR_MESSAGES.SHARE_FAILED, 'error');
            });
        }
    }

    async installApp() {
        try {
            await this.managers.pwa.installApp();
        } catch (error) {
            console.error('Install failed:', error);
            this.managers.ui.showToast('安装失败，请重试', 'error');
        }
    }
    
    takeScreenshot() {
        // 显示闪光效果
        this.managers.ui.showScreenshotFlash();

        // 使用html2canvas截图（如果可用）
        if (typeof html2canvas !== 'undefined') {
            html2canvas(document.body).then(canvas => {
                const link = document.createElement('a');
                link.download = `heart-confession-${Date.now()}.png`;
                link.href = canvas.toDataURL();
                link.click();
                this.managers.ui.showToast('截图已保存', 'success');
            }).catch(error => {
                console.error('Screenshot failed:', error);
                this.managers.ui.showToast(APP_CONFIG.ERROR_MESSAGES.SCREENSHOT_FAILED, 'error');
            });
        } else {
            this.managers.ui.showToast(APP_CONFIG.ERROR_MESSAGES.SCREENSHOT_FAILED, 'error');
        }
    }

    openSettings() {
        // 加载当前消息到输入框
        for (let i = 0; i < 6; i++) {
            const input = document.getElementById(`message-${i + 1}`);
            if (input) {
                input.value = this.messages[i];
            }
        }

        // 显示模态框
        this.managers.ui.showModal('settings-modal');
    }

    closeSettings() {
        this.managers.ui.hideModal('settings-modal');
    }
    
    saveCustomMessages() {
        const newMessages = [];
        let hasCustom = false;

        for (let i = 0; i < 6; i++) {
            const input = document.getElementById(`message-${i + 1}`);
            const message = input?.value.trim() || '';

            if (message) {
                newMessages.push(message);
                hasCustom = true;
            } else {
                newMessages.push(APP_CONFIG.DEFAULT_MESSAGES[i]);
            }
        }

        if (hasCustom) {
            try {
                this.messages = newMessages;
                this.managers.storage.setCustomMessages(this.messages);
                this.managers.ui.showToast(APP_CONFIG.SUCCESS_MESSAGES.CUSTOM_MESSAGES_SAVED, 'success');
                this.closeSettings();
            } catch (error) {
                console.error('Save failed:', error);
                this.managers.ui.showToast(APP_CONFIG.ERROR_MESSAGES.SAVE_FAILED, 'error');
            }
        } else {
            this.managers.ui.showToast('请至少输入一条消息', 'warning');
        }
    }

    resetToDefault() {
        this.messages = [...APP_CONFIG.DEFAULT_MESSAGES];
        this.managers.storage.removeCustomMessages();

        // 更新输入框
        for (let i = 0; i < 6; i++) {
            const input = document.getElementById(`message-${i + 1}`);
            if (input) {
                input.value = this.messages[i];
            }
        }

        this.managers.ui.showToast(APP_CONFIG.SUCCESS_MESSAGES.DEFAULT_MESSAGES_RESTORED, 'success');
    }
    
    loadUserPreferences() {
        // 加载自定义消息
        this.messages = this.managers.storage.getCustomMessages();

        // 加载主题偏好
        const theme = this.managers.storage.getTheme();
        this.managers.ui.applyTheme(theme);

        // 执行数据迁移
        this.managers.storage.migrate();

        console.log('User preferences loaded:', {
            messagesCount: this.messages.length,
            theme: theme
        });
    }
    
    // 清理资源
    destroy() {
        // 清理所有管理器
        Object.values(this.managers).forEach(manager => {
            if (manager && typeof manager.destroy === 'function') {
                manager.destroy();
            }
        });

        // 清理事件监听器
        window.removeEventListener('performanceOptimized', this.handlePerformanceOptimized);
        window.removeEventListener('themeApplied', this.handleThemeApplied);
        window.removeEventListener('canvasResize', this.handleCanvasResize);
        window.removeEventListener('particlesUpdate', this.handleParticlesUpdate);
        window.removeEventListener('toggleMusic', this.handleToggleMusic);
        window.removeEventListener('showToast', this.handleShowToast);

        console.log('Heart Confession App destroyed');
    }
    
}

// 全局应用实例
let heartApp = null;

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    try {
        heartApp = new HeartConfessionApp();
        console.log('Heart Confession App v2.0.0 initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Heart Confession App:', error);

        // 显示错误信息给用户
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(244, 67, 54, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10000;
        `;
        errorDiv.innerHTML = `
            <h3>应用初始化失败</h3>
            <p>请刷新页面重试</p>
            <button onclick="location.reload()" style="
                background: white;
                color: #f44336;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        document.body.appendChild(errorDiv);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (heartApp && typeof heartApp.destroy === 'function') {
        heartApp.destroy();
    }
});

// 导出应用实例（用于调试）
if (typeof window !== 'undefined') {
    window.HeartConfessionApp = HeartConfessionApp;
    window.getHeartApp = () => heartApp;
}
