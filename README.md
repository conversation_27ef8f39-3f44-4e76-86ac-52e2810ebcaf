# 心动告白 - Heart Confession App

A romantic mobile web application for expressing heartfelt confessions through interactive heart animations.

## Features

- **Interactive Heart Animation**: Click on the heart to trigger beautiful explosion animations
- **Progressive Confession System**: 6 carefully crafted confession messages with increasing emotional intensity
- **Mobile-First Design**: Optimized for all mobile devices with responsive layouts
- **Customizable Messages**: Users can create their own confession messages
- **PWA Support**: Installable as a Progressive Web App for offline access
- **Background Music**: Optional romantic background music
- **Social Sharing**: Share your confession experience on social media
- **Screenshot Capture**: Save special moments with built-in screenshot functionality
- **Local Storage**: Remembers user preferences and custom messages

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Animations**: GSAP (GreenSock Animation Platform)
- **Graphics**: HTML5 Canvas API for heart rendering
- **PWA**: Service Worker for offline functionality
- **Storage**: localStorage for user preferences

## Project Structure

```
heart-confession-app/
├── index.html              # Main HTML file
├── manifest.json          # PWA manifest
├── sw.js                  # Service Worker
├── css/
│   └── style.css         # Main stylesheet
├── js/
│   └── app.js            # Main JavaScript application
└── assets/
    ├── audio/            # Background music files
    └── icon-placeholder.svg # App icon (placeholder)
```

## Installation & Usage

1. **Local Development**:
   ```bash
   # Clone or download the project
   # Serve files using a local server (required for PWA features)
   python -m http.server 8000
   # Or use any other local server
   ```

2. **Access**: Open `http://localhost:8000` in your mobile browser

3. **PWA Installation**: 
   - Open the app in a mobile browser
   - Look for "Add to Home Screen" option
   - Install as a native app

## Core Features Implementation

### Heart Animation System
- Canvas-based heart rendering with gradient effects
- Click detection and ripple effects
- Explosion animations with floating particles
- Smooth transitions and visual feedback

### Confession Message System
- 6 pre-configured romantic messages in Chinese
- Progressive emotional intensity
- Fade-in animations for message display
- Custom message input and storage

### Mobile Optimization
- Touch-friendly interaction zones
- Responsive typography and layouts
- Performance optimization for older devices
- Battery-efficient animations

### PWA Features
- Offline functionality with service worker
- App installation capabilities
- Background sync support
- Push notification ready

## Usage Instructions

1. **Basic Usage**:
   - Open the app on your mobile device
   - Click the heart to see confession messages
   - Continue clicking to progress through all 6 messages

2. **Customization**:
   - Tap the settings gear icon (⚙️)
   - Enter your custom confession messages
   - Save to use your personalized messages

3. **Features**:
   - 🎵 Toggle background music
   - 📱 Share the app with others
   - 📸 Take screenshots of special moments

## Browser Support

- **Modern Mobile Browsers**: Chrome, Safari, Firefox, Edge
- **Required Features**: ES6+, Canvas API, Service Workers
- **Recommended**: Latest browser versions for best performance

## Performance Considerations

- Optimized for 60fps animations
- Efficient canvas rendering
- Memory management for particles
- Graceful degradation for older devices

## Localization

- Default language: Chinese (Simplified)
- Easily extensible for other languages
- Unicode emoji support for cross-platform compatibility

## Contributing

To contribute to this project:
1. Follow the existing code style and patterns
2. Test on multiple mobile devices
3. Ensure PWA functionality works correctly
4. Optimize for performance and battery usage

## License

This project is for educational and personal use. Please respect the romantic nature of the application and use it responsibly.

---

Made with ❤️ for expressing love and affection through technology.