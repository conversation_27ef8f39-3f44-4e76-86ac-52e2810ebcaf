// PWA管理模块
import { APP_CONFIG, UTILS } from './config.js';

export class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isStandalone = false;
        this.serviceWorker = null;
        
        this.init();
    }
    
    init() {
        this.checkInstallStatus();
        this.setupEventListeners();
        this.registerServiceWorker();
        this.setupInstallGuide();
    }
    
    // 检查安装状态
    checkInstallStatus() {
        // 检查是否以独立模式运行
        this.isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                           window.navigator.standalone === true;
        
        // 检查是否已安装
        this.isInstalled = this.isStandalone;
        
        console.log('PWA Status:', {
            isStandalone: this.isStandalone,
            isInstalled: this.isInstalled
        });
    }
    
    setupEventListeners() {
        // 监听安装提示事件
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
            console.log('Install prompt available');
        });
        
        // 监听应用安装事件
        window.addEventListener('appinstalled', (e) => {
            console.log('App installed successfully');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstallSuccess();
            this.deferredPrompt = null;
        });
        
        // 监听显示模式变化
        window.matchMedia('(display-mode: standalone)').addEventListener('change', (e) => {
            this.isStandalone = e.matches;
            console.log('Display mode changed:', this.isStandalone ? 'standalone' : 'browser');
        });
        
        // 监听在线/离线状态
        window.addEventListener('online', () => {
            this.handleOnlineStatus(true);
        });
        
        window.addEventListener('offline', () => {
            this.handleOnlineStatus(false);
        });
    }
    
    // 注册Service Worker
    async registerServiceWorker() {
        if (!('serviceWorker' in navigator)) {
            console.log('Service Worker not supported');
            return;
        }
        
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            this.serviceWorker = registration;
            
            console.log('[SERVICE WORKER] 注册成功', registration.scope);
            
            // 监听更新
            registration.addEventListener('updatefound', () => {
                this.handleServiceWorkerUpdate(registration);
            });
            
            // 检查是否有等待中的Service Worker
            if (registration.waiting) {
                this.showUpdateAvailable();
            }
            
        } catch (error) {
            console.error('[SERVICE WORKER] 注册失败', error);
        }
    }
    
    // 处理Service Worker更新
    handleServiceWorkerUpdate(registration) {
        const newWorker = registration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // 新版本可用
                this.showUpdateAvailable();
            }
        });
    }
    
    // 显示更新可用提示
    showUpdateAvailable() {
        const updateToast = this.createUpdateToast();
        document.body.appendChild(updateToast);
        
        // 点击更新
        updateToast.querySelector('.update-btn').addEventListener('click', () => {
            this.applyUpdate();
            document.body.removeChild(updateToast);
        });
        
        // 点击忽略
        updateToast.querySelector('.dismiss-btn').addEventListener('click', () => {
            document.body.removeChild(updateToast);
        });
    }
    
    createUpdateToast() {
        const toast = document.createElement('div');
        toast.className = 'update-toast';
        toast.innerHTML = `
            <div class="update-content">
                <span>🔄 新版本可用</span>
                <div class="update-actions">
                    <button class="update-btn">更新</button>
                    <button class="dismiss-btn">忽略</button>
                </div>
            </div>
        `;
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 300px;
        `;
        
        return toast;
    }
    
    // 应用更新
    async applyUpdate() {
        if (this.serviceWorker && this.serviceWorker.waiting) {
            this.serviceWorker.waiting.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
        }
    }
    
    // 显示安装按钮
    showInstallButton() {
        const installBtn = document.getElementById('install-btn');
        if (installBtn && !this.isInstalled) {
            installBtn.style.display = 'flex';
        }
    }
    
    // 隐藏安装按钮
    hideInstallButton() {
        const installBtn = document.getElementById('install-btn');
        if (installBtn) {
            installBtn.style.display = 'none';
        }
    }
    
    // 安装应用
    async installApp() {
        if (!this.deferredPrompt) {
            this.showInstallGuide();
            return;
        }
        
        try {
            // 显示安装提示
            this.deferredPrompt.prompt();
            
            // 等待用户选择
            const choiceResult = await this.deferredPrompt.userChoice;
            
            if (choiceResult.outcome === 'accepted') {
                console.log('用户接受了安装');
                this.showToast(APP_CONFIG.SUCCESS_MESSAGES.APP_INSTALLED, 'success');
            } else {
                console.log('用户拒绝了安装');
                this.showToast(APP_CONFIG.ERROR_MESSAGES.INSTALL_CANCELLED, 'info');
            }
            
            // 重置deferredPrompt
            this.deferredPrompt = null;
            this.hideInstallButton();
            
        } catch (error) {
            console.error('安装失败:', error);
            this.showToast('安装失败，请重试', 'error');
        }
    }
    
    // 显示安装成功消息
    showInstallSuccess() {
        this.showToast(APP_CONFIG.SUCCESS_MESSAGES.APP_INSTALLED, 'success');
    }
    
    // 设置安装引导
    setupInstallGuide() {
        if (!UTILS.isMobile() || this.isInstalled) {
            return;
        }
        
        const storage = new (await import('./StorageManager.js')).StorageManager();
        const hasShownGuide = storage.getInstallGuideShown();
        
        if (!hasShownGuide) {
            setTimeout(() => {
                this.showInstallGuide();
                storage.setInstallGuideShown(true);
            }, APP_CONFIG.PWA.INSTALL_GUIDE_DELAY);
        }
    }
    
    // 显示安装引导
    showInstallGuide() {
        const guide = this.createInstallGuide();
        document.body.appendChild(guide);
        
        // 自动消失
        setTimeout(() => {
            if (document.body.contains(guide)) {
                this.hideInstallGuide(guide);
            }
        }, 8000);
        
        // 点击关闭
        guide.querySelector('.close-btn').addEventListener('click', () => {
            this.hideInstallGuide(guide);
        });
    }
    
    createInstallGuide() {
        const guide = document.createElement('div');
        guide.className = 'install-guide';
        
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const guideText = isIOS 
            ? '点击分享按钮 📤，然后选择"添加到主屏幕"'
            : '点击浏览器菜单，选择"添加到主屏幕"或"安装应用"';
        
        guide.innerHTML = `
            <div class="guide-content">
                <div class="guide-header">
                    <span>📱 安装应用</span>
                    <button class="close-btn">×</button>
                </div>
                <p>${guideText}</p>
            </div>
        `;
        
        guide.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            animation: slideUp 0.3s ease-out;
        `;
        
        return guide;
    }
    
    hideInstallGuide(guide) {
        gsap.to(guide, {
            y: 100,
            opacity: 0,
            duration: 0.3,
            ease: "power2.out",
            onComplete: () => {
                if (document.body.contains(guide)) {
                    document.body.removeChild(guide);
                }
            }
        });
    }
    
    // 处理在线/离线状态
    handleOnlineStatus(isOnline) {
        const statusMessage = isOnline ? '网络已连接' : '网络已断开，应用将离线运行';
        const statusType = isOnline ? 'success' : 'warning';
        
        this.showToast(statusMessage, statusType);
        
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('networkStatusChanged', {
            detail: { isOnline }
        }));
    }
    
    // 显示Toast消息
    showToast(message, type = 'info') {
        window.dispatchEvent(new CustomEvent('showToast', {
            detail: { message, type }
        }));
    }
    
    // 获取PWA状态
    getStatus() {
        return {
            isInstalled: this.isInstalled,
            isStandalone: this.isStandalone,
            hasServiceWorker: !!this.serviceWorker,
            canInstall: !!this.deferredPrompt,
            isOnline: navigator.onLine
        };
    }
    
    // 获取安装统计
    getInstallStats() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            isStandalone: this.isStandalone,
            displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser',
            installPromptAvailable: !!this.deferredPrompt
        };
    }
    
    // 缓存管理
    async clearCache() {
        if ('caches' in window) {
            const cacheNames = await caches.keys();
            await Promise.all(
                cacheNames.map(cacheName => caches.delete(cacheName))
            );
            console.log('Cache cleared');
            this.showToast('缓存已清除', 'success');
        }
    }
    
    // 获取缓存大小
    async getCacheSize() {
        if (!('caches' in window)) return 0;
        
        let totalSize = 0;
        const cacheNames = await caches.keys();
        
        for (const cacheName of cacheNames) {
            const cache = await caches.open(cacheName);
            const requests = await cache.keys();
            
            for (const request of requests) {
                const response = await cache.match(request);
                if (response) {
                    const blob = await response.blob();
                    totalSize += blob.size;
                }
            }
        }
        
        return totalSize;
    }
    
    // 预缓存资源
    async precacheResources(urls) {
        if (!('caches' in window)) return;
        
        try {
            const cache = await caches.open(`${APP_CONFIG.APP_NAME}-precache`);
            await cache.addAll(urls);
            console.log('Resources precached:', urls);
        } catch (error) {
            console.error('Precaching failed:', error);
        }
    }
    
    // 销毁
    destroy() {
        // 移除事件监听器
        window.removeEventListener('beforeinstallprompt', this.handleBeforeInstallPrompt);
        window.removeEventListener('appinstalled', this.handleAppInstalled);
        window.removeEventListener('online', this.handleOnline);
        window.removeEventListener('offline', this.handleOffline);
        
        // 清理资源
        this.deferredPrompt = null;
        this.serviceWorker = null;
    }
}
