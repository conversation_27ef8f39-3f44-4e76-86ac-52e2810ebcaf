// 性能管理模块
import { APP_CONFIG, UTILS } from './config.js';

export class PerformanceManager {
    constructor() {
        this.isLowPerformanceDevice = UTILS.isLowPerformanceDevice();
        this.performanceMetrics = {
            fps: 60,
            memoryUsage: 0,
            renderTime: 0,
            animationCount: 0
        };
        
        this.observers = {
            memory: null,
            performance: null,
            intersection: null
        };
        
        this.animationFrameId = null;
        this.lastFrameTime = 0;
        this.frameCount = 0;
        this.fpsHistory = [];
        
        this.init();
    }
    
    init() {
        this.detectDeviceCapabilities();
        this.setupPerformanceMonitoring();
        this.setupMemoryManagement();
        this.setupIntersectionObserver();
        this.optimizeForDevice();
    }
    
    // 检测设备性能
    detectDeviceCapabilities() {
        const capabilities = {
            deviceMemory: navigator.deviceMemory || 4,
            hardwareConcurrency: navigator.hardwareConcurrency || 4,
            connection: navigator.connection?.effectiveType || '4g',
            userAgent: navigator.userAgent.toLowerCase(),
            pixelRatio: window.devicePixelRatio || 1
        };
        
        // 评估设备性能等级
        this.performanceLevel = this.calculatePerformanceLevel(capabilities);
        
        console.log('Device capabilities:', capabilities);
        console.log('Performance level:', this.performanceLevel);
        
        return capabilities;
    }
    
    calculatePerformanceLevel(capabilities) {
        let score = 0;
        
        // 内存评分
        if (capabilities.deviceMemory >= 8) score += 3;
        else if (capabilities.deviceMemory >= 4) score += 2;
        else score += 1;
        
        // CPU评分
        if (capabilities.hardwareConcurrency >= 8) score += 3;
        else if (capabilities.hardwareConcurrency >= 4) score += 2;
        else score += 1;
        
        // 网络评分
        if (capabilities.connection === '4g') score += 2;
        else if (capabilities.connection === '3g') score += 1;
        
        // 设备类型评分
        if (/mobile|android|iphone/i.test(capabilities.userAgent)) {
            score -= 1; // 移动设备通常性能较低
        }
        
        // 像素密度评分
        if (capabilities.pixelRatio > 2) score -= 1;
        
        if (score >= 7) return 'high';
        if (score >= 4) return 'medium';
        return 'low';
    }
    
    // 根据设备性能优化设置
    optimizeForDevice() {
        const optimizations = {
            high: {
                maxParticles: 30,
                maxExplosionParticles: 15,
                animationQuality: 'high',
                enableComplexAnimations: true,
                particlePhysics: true,
                highDPI: true
            },
            medium: {
                maxParticles: 20,
                maxExplosionParticles: 12,
                animationQuality: 'medium',
                enableComplexAnimations: true,
                particlePhysics: false,
                highDPI: true
            },
            low: {
                maxParticles: 10,
                maxExplosionParticles: 6,
                animationQuality: 'low',
                enableComplexAnimations: false,
                particlePhysics: false,
                highDPI: false
            }
        };
        
        this.settings = optimizations[this.performanceLevel];
        
        // 应用优化设置
        this.applyOptimizations();
    }
    
    applyOptimizations() {
        // 更新配置
        APP_CONFIG.PERFORMANCE.MAX_PARTICLES_HIGH = this.settings.maxParticles;
        APP_CONFIG.PERFORMANCE.MAX_EXPLOSION_PARTICLES_HIGH = this.settings.maxExplosionParticles;
        
        // 禁用高DPI支持（如果需要）
        if (!this.settings.highDPI) {
            APP_CONFIG.CANVAS.DPR_ENABLED = false;
        }
        
        // 触发优化事件
        window.dispatchEvent(new CustomEvent('performanceOptimized', {
            detail: {
                level: this.performanceLevel,
                settings: this.settings
            }
        }));
    }
    
    // 设置性能监控
    setupPerformanceMonitoring() {
        if (!window.performance) return;
        
        // FPS监控
        this.startFPSMonitoring();
        
        // 性能观察器
        if ('PerformanceObserver' in window) {
            this.observers.performance = new PerformanceObserver((list) => {
                this.handlePerformanceEntries(list.getEntries());
            });
            
            try {
                this.observers.performance.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
            } catch (e) {
                console.warn('Performance observer not fully supported');
            }
        }
    }
    
    startFPSMonitoring() {
        const measureFPS = (timestamp) => {
            if (this.lastFrameTime) {
                const delta = timestamp - this.lastFrameTime;
                const fps = 1000 / delta;
                
                this.fpsHistory.push(fps);
                if (this.fpsHistory.length > 60) {
                    this.fpsHistory.shift();
                }
                
                // 计算平均FPS
                this.performanceMetrics.fps = this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
                
                // 如果FPS过低，触发优化
                if (this.performanceMetrics.fps < 30 && this.frameCount % 60 === 0) {
                    this.handleLowFPS();
                }
            }
            
            this.lastFrameTime = timestamp;
            this.frameCount++;
            this.animationFrameId = requestAnimationFrame(measureFPS);
        };
        
        this.animationFrameId = requestAnimationFrame(measureFPS);
    }
    
    handlePerformanceEntries(entries) {
        entries.forEach(entry => {
            switch (entry.entryType) {
                case 'measure':
                    this.performanceMetrics.renderTime = entry.duration;
                    break;
                case 'navigation':
                    console.log('Navigation timing:', entry);
                    break;
                case 'paint':
                    console.log('Paint timing:', entry.name, entry.startTime);
                    break;
            }
        });
    }
    
    // 内存管理
    setupMemoryManagement() {
        // 内存监控
        if (window.performance && window.performance.memory) {
            this.monitorMemoryUsage();
        }
        
        // 定期清理
        setInterval(() => {
            this.performCleanup();
        }, APP_CONFIG.PERFORMANCE.MEMORY_CLEANUP_INTERVAL);
        
        // 页面可见性变化时的内存管理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseNonEssentialOperations();
            } else {
                this.resumeOperations();
            }
        });
    }
    
    monitorMemoryUsage() {
        const checkMemory = () => {
            if (window.performance.memory) {
                const memory = window.performance.memory;
                this.performanceMetrics.memoryUsage = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
                
                // 内存使用过高时触发清理
                if (this.performanceMetrics.memoryUsage > 0.8) {
                    this.handleHighMemoryUsage();
                }
            }
        };
        
        setInterval(checkMemory, 5000);
    }
    
    handleHighMemoryUsage() {
        console.warn('High memory usage detected, performing cleanup');
        
        // 强制垃圾回收（如果可用）
        if (window.gc) {
            window.gc();
        }
        
        // 清理粒子
        this.cleanupParticles();
        
        // 减少粒子数量
        this.settings.maxParticles = Math.max(5, this.settings.maxParticles - 5);
        this.settings.maxExplosionParticles = Math.max(3, this.settings.maxExplosionParticles - 3);
        
        // 触发内存清理事件
        window.dispatchEvent(new CustomEvent('memoryCleanup'));
    }
    
    handleLowFPS() {
        console.warn('Low FPS detected, applying optimizations');
        
        // 降低动画质量
        if (this.settings.animationQuality === 'high') {
            this.settings.animationQuality = 'medium';
        } else if (this.settings.animationQuality === 'medium') {
            this.settings.animationQuality = 'low';
        }
        
        // 减少粒子数量
        this.settings.maxParticles = Math.max(5, this.settings.maxParticles - 2);
        
        // 禁用复杂动画
        this.settings.enableComplexAnimations = false;
        
        // 触发FPS优化事件
        window.dispatchEvent(new CustomEvent('fpsOptimization', {
            detail: { fps: this.performanceMetrics.fps }
        }));
    }
    
    // 设置交叉观察器
    setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) return;
        
        this.observers.intersection = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 元素进入视口，启用动画
                    entry.target.classList.add('animate-enabled');
                } else {
                    // 元素离开视口，禁用动画
                    entry.target.classList.remove('animate-enabled');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        // 观察需要优化的元素
        document.querySelectorAll('.particle, .heart-particle').forEach(el => {
            this.observers.intersection.observe(el);
        });
    }
    
    // 清理操作
    performCleanup() {
        this.cleanupParticles();
        this.cleanupAnimations();
        this.cleanupEventListeners();
    }
    
    cleanupParticles() {
        const container = document.getElementById('particles-container');
        if (!container) return;
        
        const particles = container.querySelectorAll('.particle, .heart-particle');
        particles.forEach(particle => {
            // 移除已完成动画的粒子
            const computedStyle = window.getComputedStyle(particle);
            if (computedStyle.opacity === '0' || computedStyle.transform.includes('translateY(-')) {
                if (container.contains(particle)) {
                    container.removeChild(particle);
                }
            }
        });
    }
    
    cleanupAnimations() {
        // 清理GSAP动画
        if (window.gsap) {
            gsap.globalTimeline.getChildren().forEach(tween => {
                if (tween.progress() === 1) {
                    tween.kill();
                }
            });
        }
    }
    
    cleanupEventListeners() {
        // 移除未使用的事件监听器
        // 这里可以添加具体的清理逻辑
    }
    
    // 暂停非必要操作
    pauseNonEssentialOperations() {
        // 暂停背景动画
        if (window.gsap) {
            gsap.globalTimeline.pause();
        }
        
        // 停止FPS监控
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // 触发暂停事件
        window.dispatchEvent(new CustomEvent('operationsPaused'));
    }
    
    // 恢复操作
    resumeOperations() {
        // 恢复背景动画
        if (window.gsap) {
            gsap.globalTimeline.resume();
        }
        
        // 重启FPS监控
        this.startFPSMonitoring();
        
        // 触发恢复事件
        window.dispatchEvent(new CustomEvent('operationsResumed'));
    }
    
    // 性能测量
    measurePerformance(name, fn) {
        const startTime = performance.now();
        const result = fn();
        const endTime = performance.now();
        
        performance.mark(`${name}-start`);
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
        
        console.log(`${name} took ${endTime - startTime} milliseconds`);
        return result;
    }
    
    // 获取性能报告
    getPerformanceReport() {
        return {
            deviceLevel: this.performanceLevel,
            settings: this.settings,
            metrics: this.performanceMetrics,
            isLowPerformanceDevice: this.isLowPerformanceDevice,
            memoryInfo: window.performance.memory ? {
                used: window.performance.memory.usedJSHeapSize,
                total: window.performance.memory.totalJSHeapSize,
                limit: window.performance.memory.jsHeapSizeLimit
            } : null,
            timing: performance.timing ? {
                loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
            } : null
        };
    }
    
    // 销毁
    destroy() {
        // 停止FPS监控
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
        }
        
        // 断开观察器
        Object.values(this.observers).forEach(observer => {
            if (observer) {
                observer.disconnect();
            }
        });
        
        // 清理资源
        this.performCleanup();
    }
}
