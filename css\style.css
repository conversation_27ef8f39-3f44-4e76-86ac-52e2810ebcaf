/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #ff6b9d 0%, #c44569 50%, #f8b500 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* App container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 20px;
    max-width: 480px;
    margin: 0 auto;
}

/* Particles background */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    z-index: 10;
    position: relative;
}

.app-title {
    font-size: 2.5rem;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 10px;
    font-weight: bold;
}

.progress-indicator {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    display: inline-block;
}

#progress-text {
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;
}

/* Heart container */
.heart-container {
    position: relative;
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#heart-canvas {
    max-width: 280px;
    max-height: 280px;
    cursor: pointer;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    transition: transform 0.1s ease;
}

#heart-canvas:hover {
    transform: scale(1.05);
}

#heart-canvas:active {
    transform: scale(0.95);
}

.heart-prompt {
    margin-top: 15px;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: pulse 2s ease-in-out infinite;
}

.heart-prompt span {
    color: #c44569;
    font-weight: bold;
    font-size: 1.1rem;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Message container */
.message-container {
    width: 100%;
    max-width: 350px;
    margin-bottom: 30px;
}

.message-box {
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    text-align: center;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

#message-text {
    font-size: 1.2rem;
    color: #2d3436;
    line-height: 1.6;
    font-weight: 500;
    opacity: 0;
    animation: fadeIn 0.8s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    padding: 12px 16px;
    border-radius: 15px;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-width: 70px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn span:first-child {
    font-size: 1.2rem;
}

/* Settings button */
.settings-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 1000;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 20px;
    width: 100%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.modal-content h2 {
    color: #c44569;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.5rem;
}

.message-input-group {
    margin-bottom: 15px;
}

.message-input-group label {
    display: block;
    margin-bottom: 5px;
    color: #2d3436;
    font-weight: 500;
}

.message-input-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.message-input-group input:focus {
    outline: none;
    border-color: #ff6b9d;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.btn-primary {
    background: #ff6b9d;
    color: white;
}

.btn-primary:hover {
    background: #e55a87;
}

.btn-secondary {
    background: #ddd;
    color: #2d3436;
}

.btn-secondary:hover {
    background: #ccc;
}

/* Responsive design */
@media (max-width: 480px) {
    .app-container {
        padding: 15px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    #heart-canvas {
        max-width: 240px;
        max-height: 240px;
    }
    
    .message-box {
        padding: 15px;
    }
    
    #message-text {
        font-size: 1.1rem;
    }
    
    .action-buttons {
        gap: 10px;
    }
    
    .action-btn {
        min-width: 60px;
        padding: 10px 12px;
    }
    
    .modal-content {
        padding: 20px;
    }
}

@media (max-width: 360px) {
    .app-title {
        font-size: 1.8rem;
    }
    
    #heart-canvas {
        max-width: 200px;
        max-height: 200px;
    }
    
    #message-text {
        font-size: 1rem;
    }
    
    .action-btn {
        font-size: 0.8rem;
        min-width: 50px;
    }
}

/* Animations for heart explosion */
.heart-explode {
    animation: explode 0.6s ease-out forwards;
}

@keyframes explode {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Share animation */
.share-animation {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Screenshot flash effect */
.screenshot-flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
    opacity: 0;
    pointer-events: none;
    animation: flash 0.3s ease-out;
}

@keyframes flash {
    0% { opacity: 0; }
    50% { opacity: 0.8; }
    100% { opacity: 0; }
}

/* Music visualization */
.music-active {
    animation: musicPulse 1s ease-in-out infinite;
}

@keyframes musicPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Heart particles */
.heart-particle {
    position: absolute;
    color: #ff6b9d;
    font-size: 1.5rem;
    pointer-events: none;
    z-index: 100;
    animation: heartFloat 2s ease-out forwards;
}

@keyframes heartFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(0.5);
    }
}