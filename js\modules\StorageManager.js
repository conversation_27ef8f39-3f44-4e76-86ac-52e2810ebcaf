// 存储管理模块
import { APP_CONFIG } from './config.js';

export class StorageManager {
    constructor() {
        this.isSupported = this.checkSupport();
        this.cache = new Map();
    }
    
    // 检查localStorage支持
    checkSupport() {
        try {
            const test = '__storage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            console.warn('localStorage not supported:', e);
            return false;
        }
    }
    
    // 设置数据
    set(key, value) {
        if (!this.isSupported) {
            this.cache.set(key, value);
            return;
        }
        
        try {
            const data = {
                value,
                timestamp: Date.now(),
                version: APP_CONFIG.VERSION
            };
            localStorage.setItem(key, JSON.stringify(data));
            this.cache.set(key, value);
        } catch (e) {
            console.error('Failed to save to localStorage:', e);
            this.cache.set(key, value);
        }
    }
    
    // 获取数据
    get(key, defaultValue = null) {
        // 先从缓存获取
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        if (!this.isSupported) {
            return defaultValue;
        }
        
        try {
            const item = localStorage.getItem(key);
            if (!item) return defaultValue;
            
            const data = JSON.parse(item);
            
            // 检查版本兼容性
            if (data.version && data.version !== APP_CONFIG.VERSION) {
                console.warn(`Version mismatch for ${key}, using default value`);
                return defaultValue;
            }
            
            this.cache.set(key, data.value);
            return data.value;
        } catch (e) {
            console.error('Failed to read from localStorage:', e);
            return defaultValue;
        }
    }
    
    // 删除数据
    remove(key) {
        this.cache.delete(key);
        
        if (this.isSupported) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('Failed to remove from localStorage:', e);
            }
        }
    }
    
    // 清除所有数据
    clear() {
        this.cache.clear();
        
        if (this.isSupported) {
            try {
                // 只清除应用相关的数据
                Object.values(APP_CONFIG.STORAGE_KEYS).forEach(key => {
                    localStorage.removeItem(key);
                });
            } catch (e) {
                console.error('Failed to clear localStorage:', e);
            }
        }
    }
    
    // 检查键是否存在
    has(key) {
        return this.cache.has(key) || (this.isSupported && localStorage.getItem(key) !== null);
    }
    
    // 获取所有键
    keys() {
        if (!this.isSupported) {
            return Array.from(this.cache.keys());
        }
        
        const keys = [];
        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (Object.values(APP_CONFIG.STORAGE_KEYS).includes(key)) {
                    keys.push(key);
                }
            }
        } catch (e) {
            console.error('Failed to get keys from localStorage:', e);
        }
        
        return keys;
    }
    
    // 获取存储大小
    getSize() {
        if (!this.isSupported) {
            return this.cache.size;
        }
        
        let size = 0;
        try {
            Object.values(APP_CONFIG.STORAGE_KEYS).forEach(key => {
                const item = localStorage.getItem(key);
                if (item) {
                    size += item.length;
                }
            });
        } catch (e) {
            console.error('Failed to calculate storage size:', e);
        }
        
        return size;
    }
    
    // 音乐偏好设置
    setMusicEnabled(enabled) {
        this.set(APP_CONFIG.STORAGE_KEYS.MUSIC_ENABLED, enabled);
    }
    
    getMusicEnabled() {
        return this.get(APP_CONFIG.STORAGE_KEYS.MUSIC_ENABLED, false);
    }
    
    // 自定义消息
    setCustomMessages(messages) {
        if (!Array.isArray(messages) || messages.length !== 6) {
            throw new Error('Messages must be an array of 6 strings');
        }
        this.set(APP_CONFIG.STORAGE_KEYS.CUSTOM_MESSAGES, messages);
    }
    
    getCustomMessages() {
        return this.get(APP_CONFIG.STORAGE_KEYS.CUSTOM_MESSAGES, APP_CONFIG.DEFAULT_MESSAGES);
    }
    
    removeCustomMessages() {
        this.remove(APP_CONFIG.STORAGE_KEYS.CUSTOM_MESSAGES);
    }
    
    // 主题偏好
    setTheme(theme) {
        if (!APP_CONFIG.THEMES[theme]) {
            throw new Error(`Invalid theme: ${theme}`);
        }
        this.set(APP_CONFIG.STORAGE_KEYS.THEME_PREFERENCE, theme);
    }
    
    getTheme() {
        return this.get(APP_CONFIG.STORAGE_KEYS.THEME_PREFERENCE, 'romantic');
    }
    
    // 安装引导状态
    setInstallGuideShown(shown) {
        this.set(APP_CONFIG.STORAGE_KEYS.INSTALL_GUIDE_SHOWN, shown);
    }
    
    getInstallGuideShown() {
        return this.get(APP_CONFIG.STORAGE_KEYS.INSTALL_GUIDE_SHOWN, false);
    }
    
    // 用户偏好设置
    setUserPreferences(preferences) {
        const currentPrefs = this.getUserPreferences();
        const updatedPrefs = { ...currentPrefs, ...preferences };
        this.set(APP_CONFIG.STORAGE_KEYS.USER_PREFERENCES, updatedPrefs);
    }
    
    getUserPreferences() {
        return this.get(APP_CONFIG.STORAGE_KEYS.USER_PREFERENCES, {
            theme: 'romantic',
            musicEnabled: false,
            animationsEnabled: true,
            particlesEnabled: true,
            autoPlay: false,
            volume: APP_CONFIG.AUDIO.DEFAULT_VOLUME
        });
    }
    
    // 批量操作
    setBatch(data) {
        Object.entries(data).forEach(([key, value]) => {
            this.set(key, value);
        });
    }
    
    getBatch(keys) {
        const result = {};
        keys.forEach(key => {
            result[key] = this.get(key);
        });
        return result;
    }
    
    // 数据导出
    export() {
        const data = {};
        this.keys().forEach(key => {
            data[key] = this.get(key);
        });
        
        return {
            version: APP_CONFIG.VERSION,
            timestamp: Date.now(),
            data
        };
    }
    
    // 数据导入
    import(exportData) {
        if (!exportData || !exportData.data) {
            throw new Error('Invalid export data');
        }
        
        // 检查版本兼容性
        if (exportData.version !== APP_CONFIG.VERSION) {
            console.warn('Version mismatch in import data');
        }
        
        try {
            Object.entries(exportData.data).forEach(([key, value]) => {
                this.set(key, value);
            });
            return true;
        } catch (e) {
            console.error('Failed to import data:', e);
            return false;
        }
    }
    
    // 数据迁移
    migrate() {
        const currentVersion = this.get('app_version', '1.0.0');
        
        if (currentVersion === APP_CONFIG.VERSION) {
            return; // 无需迁移
        }
        
        console.log(`Migrating data from ${currentVersion} to ${APP_CONFIG.VERSION}`);
        
        // 执行版本特定的迁移逻辑
        this.performMigration(currentVersion, APP_CONFIG.VERSION);
        
        // 更新版本号
        this.set('app_version', APP_CONFIG.VERSION);
    }
    
    performMigration(fromVersion, toVersion) {
        // 这里可以添加具体的迁移逻辑
        // 例如：重命名键、转换数据格式等
        
        if (fromVersion === '1.0.0' && toVersion === '2.0.0') {
            // 示例迁移：将旧的存储键迁移到新的键
            const oldMusicKey = 'heartapp_music_enabled';
            const newMusicKey = APP_CONFIG.STORAGE_KEYS.MUSIC_ENABLED;
            
            if (this.isSupported && localStorage.getItem(oldMusicKey)) {
                const value = localStorage.getItem(oldMusicKey);
                this.set(newMusicKey, value === 'true');
                localStorage.removeItem(oldMusicKey);
            }
        }
    }
    
    // 清理过期数据
    cleanup() {
        const now = Date.now();
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        
        this.keys().forEach(key => {
            try {
                const item = localStorage.getItem(key);
                if (item) {
                    const data = JSON.parse(item);
                    if (data.timestamp && (now - data.timestamp) > maxAge) {
                        this.remove(key);
                        console.log(`Cleaned up expired data: ${key}`);
                    }
                }
            } catch (e) {
                // 忽略解析错误，可能是旧格式数据
            }
        });
    }
    
    // 获取存储统计信息
    getStats() {
        return {
            isSupported: this.isSupported,
            keyCount: this.keys().length,
            cacheSize: this.cache.size,
            storageSize: this.getSize(),
            version: APP_CONFIG.VERSION
        };
    }
}
