<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b9d;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ff8fab;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffa8cc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#heartGradient)" stroke="#fff" stroke-width="10"/>
  
  <!-- Heart shape -->
  <path d="M256 400 
           C 256 400, 160 320, 160 240
           C 160 200, 200 160, 240 160
           C 248 160, 256 168, 256 176
           C 256 168, 264 160, 272 160
           C 312 160, 352 200, 352 240
           C 352 320, 256 400, 256 400 Z" 
        fill="#fff" 
        stroke="#ff6b9d" 
        stroke-width="4"/>
  
  <!-- Highlight on heart -->
  <ellipse cx="220" cy="200" rx="20" ry="15" fill="rgba(255,255,255,0.6)" opacity="0.8"/>
  
  <!-- Small hearts around -->
  <text x="180" y="120" font-family="Arial" font-size="24" fill="#fff" opacity="0.7">💕</text>
  <text x="320" y="140" font-family="Arial" font-size="20" fill="#fff" opacity="0.7">💖</text>
  <text x="380" y="280" font-family="Arial" font-size="18" fill="#fff" opacity="0.7">💕</text>
  <text x="120" y="300" font-family="Arial" font-size="22" fill="#fff" opacity="0.7">💖</text>
  <text x="140" y="420" font-family="Arial" font-size="16" fill="#fff" opacity="0.7">💕</text>
  <text x="360" y="400" font-family="Arial" font-size="18" fill="#fff" opacity="0.7">💖</text>
</svg>