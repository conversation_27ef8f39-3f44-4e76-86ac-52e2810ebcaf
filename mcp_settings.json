{"inputs": [{"type": "promptString", "id": "<PERSON><PERSON><PERSON><PERSON>", "description": "2m1dFwzUjHomtTPVv3CTScInT", "password": true}, {"type": "promptString", "id": "graphitiApiKey", "description": "Graphiti API Key for memory management", "password": true}], "servers": {"@21st-dev/magic": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest"], "env": {"API_KEY": "${input:api<PERSON><PERSON>}"}}, "graphiti-mcp": {"command": "npx", "args": ["-y", "graphiti-mcp@latest"], "env": {"GRAPHITI_API_KEY": "${input:graphitiApiKey}", "GRAPHITI_BASE_URL": "https://api.graphiti.ai", "GRAPHITI_ENVIRONMENT": "production"}}}}